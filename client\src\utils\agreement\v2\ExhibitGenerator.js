/**
 * Exhibit Generator for Agreement System V2
 * 
 * Generates dynamic Exhibit I (Specifications) and Exhibit II (Milestones)
 * based on actual user input data, project requirements, and milestone systems.
 */

export class ExhibitGenerator {
  constructor() {
    this.projectTypeTemplates = {
      game: this.getGameExhibitTemplate(),
      software: this.getSoftwareExhibitTemplate(),
      music: this.getMusicExhibitTemplate(),
      film: this.getFilmExhibitTemplate(),
      art: this.getArtExhibitTemplate()
    };
  }

  /**
   * Generate Exhibit I (Specifications) based on project data
   */
  generateExhibitI(projectData) {
    const { projectType, name, description, specifications = {} } = projectData;
    
    let exhibit = `## EXHIBIT I\n### SPECIFICATIONS\n\n`;
    exhibit += `**${name} - Project Specifications**\n\n`;
    
    // Project Overview
    exhibit += `**Project Overview:**\n`;
    exhibit += `${description}\n\n`;
    
    // Core Features (if provided)
    if (specifications.coreFeatures && specifications.coreFeatures.length > 0) {
      exhibit += `**Core Features:**\n\n`;
      specifications.coreFeatures.forEach((feature, index) => {
        exhibit += `${index + 1}. **${feature.name}**\n`;
        if (feature.description) {
          exhibit += `   ${feature.description}\n`;
        }
        if (feature.details && feature.details.length > 0) {
          feature.details.forEach(detail => {
            exhibit += `   - ${detail}\n`;
          });
        }
        exhibit += `\n`;
      });
    }
    
    // Technical Requirements
    exhibit += `**Technical Requirements:**\n`;
    if (specifications.technical) {
      Object.entries(specifications.technical).forEach(([key, value]) => {
        exhibit += `- ${this.formatTechnicalKey(key)}: ${value}\n`;
      });
    } else {
      // Default technical requirements based on project type
      const defaultTech = this.getDefaultTechnicalRequirements(projectType);
      defaultTech.forEach(req => {
        exhibit += `- ${req}\n`;
      });
    }
    exhibit += `\n`;
    
    // Platform Requirements (for games/software)
    if (['game', 'software'].includes(projectType) && specifications.platforms) {
      exhibit += `**Platform Requirements:**\n`;
      specifications.platforms.forEach(platform => {
        exhibit += `- ${platform}\n`;
      });
      exhibit += `\n`;
    }
    
    // Art/Audio Requirements (for creative projects)
    if (['game', 'film', 'art', 'music'].includes(projectType)) {
      if (specifications.artStyle) {
        exhibit += `**Art Style:** ${specifications.artStyle}\n`;
      }
      if (specifications.audioRequirements) {
        exhibit += `**Audio Requirements:** ${specifications.audioRequirements}\n`;
      }
      exhibit += `\n`;
    }
    
    // Deliverables
    exhibit += `**Deliverables:**\n`;
    if (specifications.deliverables && specifications.deliverables.length > 0) {
      specifications.deliverables.forEach(deliverable => {
        exhibit += `- ${deliverable}\n`;
      });
    } else {
      const defaultDeliverables = this.getDefaultDeliverables(projectType);
      defaultDeliverables.forEach(deliverable => {
        exhibit += `- ${deliverable}\n`;
      });
    }
    
    return exhibit;
  }

  /**
   * Generate Exhibit II (Milestones) based on project roadmap
   */
  generateExhibitII(projectData, milestonesData = null) {
    const { name } = projectData;
    
    let exhibit = `## EXHIBIT II\n### PRODUCT ROADMAP\n\n`;
    exhibit += `**${name} - Development Roadmap**\n\n`;
    
    if (milestonesData && milestonesData.phases) {
      // Use provided milestone data
      milestonesData.phases.forEach(phase => {
        exhibit += `**${phase.name}**\n`;
        if (phase.duration) {
          exhibit += `*Duration: ${phase.duration}*\n`;
        }
        phase.tasks.forEach(task => {
          exhibit += `- ${task}\n`;
        });
        exhibit += `\n`;
      });
      
      // Add specific milestones if provided
      if (milestonesData.milestones) {
        exhibit += `**Milestones:**\n\n`;
        milestonesData.milestones.forEach((milestone, index) => {
          exhibit += `${index + 1}. **${milestone.name}**\n`;
          if (milestone.deadline) {
            exhibit += `   - Deadline: ${milestone.deadline}\n`;
          }
          if (milestone.deliverables) {
            milestone.deliverables.forEach(deliverable => {
              exhibit += `   - ${deliverable}\n`;
            });
          }
          exhibit += `\n`;
        });
      }
    } else {
      // Generate default roadmap based on project type
      const defaultRoadmap = this.getDefaultRoadmap(projectData.projectType);
      defaultRoadmap.forEach(phase => {
        exhibit += `**${phase.name}**\n`;
        phase.tasks.forEach(task => {
          exhibit += `- ${task}\n`;
        });
        exhibit += `\n`;
      });
    }
    
    return exhibit;
  }

  /**
   * Generate exhibits for recreating the lawyer example 1:1
   */
  generateLawyerExampleExhibits() {
    // Exact data from the lawyer example
    const villageOfTheAgesData = {
      project: {
        name: 'Village of The Ages',
        description: 'Village of The Ages is a village simulation game where players guide communities through historical progressions and manage resource-based challenges. The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
        projectType: 'game',
        specifications: {
          coreFeatures: [
            {
              name: 'Village Building & Management',
              details: [
                'Resource gathering and management',
                'Building placement and upgrade systems',
                'Population growth and happiness mechanics',
                'Economic systems and trade routes'
              ]
            },
            {
              name: 'Historical Progression',
              details: [
                'Era-based technology trees',
                'Cultural and social evolution',
                'Historical events and their impacts',
                'Adaptation to changing times'
              ]
            },
            {
              name: 'Resource-Based Challenges',
              details: [
                'Seasonal resource variations',
                'Natural disaster management',
                'Scarcity-driven decision making',
                'Environmental adaptation'
              ]
            },
            {
              name: 'Dynamic Challenge System',
              details: [
                'Procedurally generated events',
                'Adaptive difficulty based on player performance',
                'Multiple solution paths for challenges',
                'Long-term consequence systems'
              ]
            }
          ],
          technical: {
            'Engine': 'Unreal Engine 5',
            'Minimum Specs': '[To be detailed in technical documentation]',
            'Art Style': 'Stylized, readable visuals with distinctive era-appropriate aesthetics',
            'Audio': 'Atmospheric soundtrack that evolves with historical periods'
          }
        }
      },
      milestones: {
        phases: [
          {
            name: 'Phase 1: Core Gameplay Development (Months 1-2)',
            tasks: [
              'Basic village layout and building system',
              'Core resource gathering mechanics',
              'Initial AI for villagers',
              'Basic UI framework',
              'First playable prototype with one historical era'
            ]
          },
          {
            name: 'Phase 2: Feature Expansion (Months 2-3)',
            tasks: [
              'Additional historical eras',
              'Enhanced resource management systems',
              'Weather and disaster systems',
              'Trading mechanics',
              'Technology progression system'
            ]
          },
          {
            name: 'Phase 3: Polish and Enhancement (Month 4)',
            tasks: [
              'UI refinement',
              'Performance optimization',
              'Additional content (buildings, resources, etc.)',
              'Balancing and gameplay tuning',
              'Audio implementation'
            ]
          },
          {
            name: 'Phase 4: Testing and Launch Preparation (Month 4)',
            tasks: [
              'QA testing',
              'Bug fixing',
              'Marketing assets preparation',
              'Steam/Epic Games Store setup',
              'Early access launch'
            ]
          }
        ],
        milestones: [
          {
            name: 'Milestone 1: First Playable (End of Month 1)',
            deliverables: [
              'Core mechanics implemented',
              'Basic building and resource systems functional',
              'First era playable'
            ]
          },
          {
            name: 'Milestone 2: Feature Complete (End of Month 3)',
            deliverables: [
              'All core features implemented',
              'Multiple eras playable',
              'Core systems integrated'
            ]
          },
          {
            name: 'Milestone 3: Release Candidate (Mid-Month 4)',
            deliverables: [
              'All features polished',
              'Performance optimized',
              'Major bugs resolved'
            ]
          },
          {
            name: 'Milestone 4: Launch (End of Month 4)',
            deliverables: [
              'Game ready for early access release',
              'Store page and marketing assets complete',
              'Launch plan executed'
            ]
          }
        ]
      }
    };

    return {
      exhibitI: this.generateExhibitI(villageOfTheAgesData.project),
      exhibitII: this.generateExhibitII(villageOfTheAgesData.project, villageOfTheAgesData.milestones)
    };
  }

  // Helper methods for default templates
  getDefaultTechnicalRequirements(projectType) {
    const defaults = {
      game: [
        'Game engine and development tools',
        'Platform requirements (PC, console, mobile)',
        'Performance specifications and optimization targets',
        'Art style and technical art requirements',
        'Audio and music specifications'
      ],
      software: [
        'Programming languages and frameworks',
        'Database and infrastructure requirements',
        'Performance and scalability specifications',
        'Security and compliance requirements',
        'Integration requirements with existing systems'
      ],
      music: [
        'Audio format and quality specifications',
        'Recording and production requirements',
        'Instrumentation and arrangement specifications',
        'Mixing and mastering requirements'
      ],
      film: [
        'Video format and resolution specifications',
        'Equipment and software requirements',
        'Post-production and editing specifications',
        'Audio and visual effects requirements'
      ],
      art: [
        'Medium and format specifications',
        'Size and resolution requirements',
        'Style and aesthetic guidelines',
        'Delivery format specifications'
      ]
    };
    return defaults[projectType] || defaults.software;
  }

  getDefaultDeliverables(projectType) {
    const defaults = {
      game: [
        'Playable game build',
        'Source code and project files',
        'Art assets and documentation',
        'Audio files and implementation',
        'Testing and QA documentation'
      ],
      software: [
        'Working software application',
        'Source code and documentation',
        'User manuals and guides',
        'Testing documentation',
        'Deployment instructions'
      ],
      music: [
        'Final audio tracks in required formats',
        'Source files and project sessions',
        'Sheet music or notation (if applicable)',
        'Mixing and mastering documentation'
      ],
      film: [
        'Final video in required formats',
        'Source footage and project files',
        'Post-production documentation',
        'Audio tracks and mixing files'
      ],
      art: [
        'Final artwork in required formats',
        'Source files and working documents',
        'Style guides and documentation',
        'Asset variations and formats'
      ]
    };
    return defaults[projectType] || defaults.software;
  }

  getDefaultRoadmap(projectType) {
    // Simplified default roadmaps - in practice these would be more detailed
    const roadmaps = {
      game: [
        {
          name: 'Phase 1: Core Development',
          tasks: ['Basic mechanics', 'Core systems', 'Initial prototype']
        },
        {
          name: 'Phase 2: Feature Implementation',
          tasks: ['Advanced features', 'Content creation', 'System integration']
        },
        {
          name: 'Phase 3: Polish and Testing',
          tasks: ['Bug fixes', 'Performance optimization', 'Quality assurance']
        },
        {
          name: 'Phase 4: Launch Preparation',
          tasks: ['Final testing', 'Marketing preparation', 'Release deployment']
        }
      ],
      software: [
        {
          name: 'Phase 1: Foundation',
          tasks: ['Architecture design', 'Core functionality', 'Basic UI']
        },
        {
          name: 'Phase 2: Feature Development',
          tasks: ['Advanced features', 'Integration', 'Testing']
        },
        {
          name: 'Phase 3: Refinement',
          tasks: ['Bug fixes', 'Performance optimization', 'Documentation']
        },
        {
          name: 'Phase 4: Deployment',
          tasks: ['Final testing', 'Deployment preparation', 'Launch']
        }
      ]
    };
    return roadmaps[projectType] || roadmaps.software;
  }

  formatTechnicalKey(key) {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  }

  // Placeholder methods for other project types
  getGameExhibitTemplate() { return {}; }
  getSoftwareExhibitTemplate() { return {}; }
  getMusicExhibitTemplate() { return {}; }
  getFilmExhibitTemplate() { return {}; }
  getArtExhibitTemplate() { return {}; }
}
